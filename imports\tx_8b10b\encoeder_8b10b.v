`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    10:16:04 10/18/2013 
// Design Name: 
// Module Name:    encoeder_8b10b 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module  encoder_8b10b (data_in,clk,rst,tk,rd_in,data_out,rd_out,en_data_in
    );
input  [7:0]  data_in;
input  en_data_in;
input  [1:0]  rd_in;
input  clk;
input  rst;
input  tk;
output  [9:0]  data_out;
output  [1:0]  rd_out;

 wire  [1:0]  rd_in;
 wire  [9:0]  d_data_out;
 wire  [9:0]  k_data_out;
 wire  [1:0]  d_rd_out;
 wire  [1:0]  k_rd_out;

 reg  [7:0] d_data_in;
 reg  [7:0] k_data_in;
 reg  [9:0] data_out_i;
 reg  [9:0] data_out;
 //reg  [1:0] rd_out_i;
 reg  [1:0] rd_out;
 reg   tk_s1;
 reg   tk_s2;
 reg   tk_s3;
 reg   tk_s4;

encoder  d_encoder (.clk(clk),
                    .rst(rst),
						  .data_in(d_data_in),
						  .en_data_in(en_data_in),
						  .rd_in(rd_in),
						  .data_out(d_data_out),
						  .rd_out_i(d_rd_out));
						  
k_encoder  k_encoder (.clk(clk),
                      .rst(rst),
						    .data_in(k_data_in),
							 .en_data_in(en_data_in),
						    .rd_in(rd_in),
						    .data_out(k_data_out),
						    .rd_out(k_rd_out));
						  
always @(posedge clk)	begin
    if (!en_data_in) begin
	 d_data_in <= 8'b0;
	 k_data_in <= 8'b0;
	 end
	 else begin
		if(tk) begin
			k_data_in <= data_in;
			d_data_in <= 8'b0;
		end	
		else begin
			d_data_in <= data_in;
			k_data_in <= 8'b0;
		end
	 end
end

always @(posedge clk)	begin
    if (!rst) begin
	    tk_s1 <= 0;
		tk_s2 <= 0;
		tk_s3 <= 0;
		tk_s4 <= 0;
	 end
	 else begin
	    tk_s1 <= tk;
		tk_s2 <= tk_s1;
		tk_s3 <= tk_s2;
		tk_s4 <= tk_s3;
	 end
end




always @(posedge clk)	begin
		if(!rst)	begin
		data_out <= 10'b0;
		//rd_out   <= 2'b00;
		end
		else begin
		data_out <= data_out_i;
		//rd_out   <= rd_out_i;
		end
end

always @(d_rd_out or k_rd_out or tk_s4)	begin
		if(tk_s4) begin
		rd_out <= k_rd_out;	
			end
		else begin
		rd_out <= d_rd_out;
		end
end

always @(d_data_out or k_data_out or tk_s4)	begin
		if(tk_s4) begin
		data_out_i <= k_data_out;	
		end
		else begin
		data_out_i <= d_data_out;
		end
end

endmodule
