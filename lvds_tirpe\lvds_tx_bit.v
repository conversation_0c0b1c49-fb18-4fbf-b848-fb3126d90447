// *********************************************************************************
// * 文件名: lvds_tx_bit.v
// * 创建日期: 2025.07.23
// * 公司: NSSC
// * 作者: egdon
// * 描述: 物理层，只实现lvds三线制的串并转换，速率可设置，使用双缓冲机制
// * 修改记录:
// * - 2025.07.23 - v1.0 - 初版本
// *********************************************************************************
module lvds_tx_bit(
    input clk,
    input rst_n,
    //用户接口
    input tx_valid,
    input [7:0] data_in,
    output wr_clk,
    output tx_ready,
    //phy层接口
    output wire lvds_tx_clk,
    output reg  lvds_tx_data,
    output reg  lvds_tx_stb_n
);

wire tx_clk,tx_clk_buf;
assign tx_clk = ~tx_clk_buf; //下降沿传输
assign wr_clk = tx_clk; //提供给用户
assign lvds_tx_clk = tx_clk; //提供给phy层

wire locked;
reg tx_rst_n,tx_rst_n_buf;

reg [7:0] tx_buf_0,tx_buf_1; //双缓冲
reg [1:0] tx_buf_rdy; //缓冲区状态
reg tx_rd_ff,tx_tx_ff; //读取、发送标志
reg [2:0] tx_cnt; //发送计数器

clk_lvds_tx clk_lvds_tx
(
    .clk_out1(tx_clk_buf),    
    .reset(~rst_n), 
    .locked(locked),      
    .clk_in1(clk)
);     

//同步低电平复位
always@(posedge tx_clk) begin
    if((!rst_n)||(!locked)) begin
        tx_rst_n_buf <= 1'b0;
    end
    else begin
        tx_rst_n_buf <= 1'b1;
    end
end

always@(posedge tx_clk) tx_rst_n <= tx_rst_n_buf;

assign tx_ready = ~(&tx_buf_rdy); 

always@(posedge tx_clk or negedge tx_rst_n) begin
    if(!tx_rst_n) begin
        tx_buf_0 <= 8'b0;
        tx_buf_1 <= 8'b0;
        tx_buf_rdy <= 2'b0;
        tx_rd_ff <= 1'b0;
        tx_tx_ff <= 1'b0;
        tx_cnt <= 3'b0;
        lvds_tx_stb_n <= 1'b1;
    end
    else begin
        // 双缓冲中只要有一个空就写入
        if(~(&tx_buf_rdy)) begin
            if(tx_valid) begin
                tx_rd_ff <= ~tx_rd_ff;
                if(tx_rd_ff) begin
                    tx_buf_1 <= data_in;
                    tx_buf_rdy[1] <= 1'b1;
                end
                else begin
                    tx_buf_0 <= data_in;
                    tx_buf_rdy[0] <= 1'b1;
                end
            end
        end
       // 双缓冲中只要有一个不空就读出
        if(|tx_buf_rdy) begin
            lvds_tx_stb_n <= 1'b0;
            tx_cnt <= tx_cnt + 1'b1;
            if(tx_tx_ff) begin
                lvds_tx_data <= tx_buf_1[7];
                tx_buf_1 <= tx_buf_1 << 1;
            end    
            else begin
                lvds_tx_data <= tx_buf_0[7];
                tx_buf_0 <= tx_buf_0 << 1;
            end   
           
            if(tx_cnt == 3'd7)begin
                tx_tx_ff <= ~tx_tx_ff;
                if(tx_tx_ff)
                 tx_buf_rdy[1] <= 1'b0;
                else
                 tx_buf_rdy[0] <= 1'b0;
            end
        end
        else begin
            lvds_tx_stb_n <= 1'b1;
            lvds_tx_data <= 1'b0;
        end
    end
end

endmodule