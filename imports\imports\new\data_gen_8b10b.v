module data_gen_8b10b(
    input wire[10:0] pl_insight, //应用标识号
    input wire sci_gen,          //开始生成科学数据的信号
    input wire clk,              // 时钟信号
    input wire rst_n,            // 异步复位信号，低电平有效
    input wire fifo_tready,       // FIFO 就绪信号
    input wire[15:0]sci_data_len,
    input wire[31:0] pack_num,
    input wire [31:0] wait_time,
    input wire cpu_wen,
    input wire [7:0] cpu_addr,
    input wire [31:0] cpu_din,
    output reg [7:0] fifo_tdata, // FIFO数据输入
    output reg fifo_tvalid,      // FIFO 数据有效信号
    output reg [0:0]s_axis_tid,
    output reg fifo_tlast,
    output reg [3:0] state_monitor
);
reg [23:0] time_cnt;
reg [13:0] pack_cnt;
reg [15:0] cnt;
reg [15:0] sum_check;
reg [2:0] time_code_cnt; 
reg [9:0] write_n_cnt;
reg sci_gen_valid;
reg [31:0] pack_num_reg;
reg [15:0] sci_data_len_reg;
reg [31:0] wait_time_reg;
reg [10:0] pl_insight_reg;

    // 状态定义
    localparam INI  = 4'b0000;  // 空闲状态
    localparam START  = 4'b0001;
    localparam WRITE_EB  = 4'b0010; // 写入 0xEB
    localparam WRITE_90  = 4'b0011; // 写入 0x90
    localparam WRITE_ver_and_sig1 = 4'b0100; // 写入 版本号和应用标识符
    localparam WRITE_ver_and_sig2 = 4'b0101; 
    localparam WRITE_pack_ctrl1 = 4'b0110; //写入包序控制
    localparam WRITE_pack_ctrl2 = 4'b0111; 
    localparam WRITE_sci_data_len1 = 4'b1000; //写入科学数据长度
    localparam WRITE_sci_data_len2 = 4'b1001;
    localparam WRITE_time_code = 4'b1010;//写入时间码48位0
    localparam WRITE_N   = 4'b1011; // 写入 N 个 8 位数据
    localparam WRITE_sum1 = 4'b1100; //写入时间码和科学数据按字节之和
    localparam WRITE_sum2 = 4'b1101; //写入时间码和科学数据按字节之和
    localparam WAIT  = 4'b1110;  // 等待状态
    localparam DONE  = 4'b1111;  // 完成状态
    
    reg [3:0] current_state, next_state; // 状态寄存器
    reg [7:0] sci_data;

    // 状态机寄存器
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            current_state <= INI;
            state_monitor <= INI;
            sci_gen_valid <= 1'b0;
        end else begin
            current_state <= next_state;
            state_monitor <= current_state;
            if (!cpu_wen && cpu_addr == 4'b0000 && cpu_din == 32'hFF000000) begin
                sci_gen_valid <= 1'b1;
            end else begin
                sci_gen_valid <= 1'b0;  // 空闲时清零
            end
        end
    end
    
    // 状态转移逻辑
    always @(*) begin
        case (current_state)
            INI: begin
                if (sci_gen_valid) begin 
                    next_state = START;
                end else begin
                    next_state = INI;
                end
            end
            START: begin
                if (fifo_tready) begin
                    next_state = WRITE_EB;
                end else begin
                    next_state = START;
                end
            end
            WRITE_EB: begin
                if (fifo_tready) begin
                    next_state = WRITE_90;
                end else begin
                    next_state = WRITE_EB;
                end
            end
            WRITE_90: begin
                if (fifo_tready) begin
                    next_state = WRITE_ver_and_sig1;
                end else begin
                    next_state = WRITE_90;
                end
            end
            WRITE_ver_and_sig1: begin
                if (fifo_tready) begin
                    next_state = WRITE_ver_and_sig2;
                end else begin
                    next_state = WRITE_ver_and_sig1;
                end
            end
            WRITE_ver_and_sig2: begin
                if (fifo_tready) begin
                    next_state = WRITE_pack_ctrl1;
                end else begin
                    next_state = WRITE_ver_and_sig2;
                end
            end
            WRITE_pack_ctrl1: begin
                if (fifo_tready) begin
                    next_state = WRITE_pack_ctrl2;
                end else begin
                    next_state = WRITE_pack_ctrl1;
                end
            end
            WRITE_pack_ctrl2: begin
                if (fifo_tready) begin
                    next_state = WRITE_sci_data_len1;
                end else begin
                    next_state = WRITE_pack_ctrl2;
                end
            end
            WRITE_sci_data_len1: begin
                if (fifo_tready) begin
                    next_state = WRITE_sci_data_len2;
                end else begin
                    next_state = WRITE_sci_data_len1; // 等待 FIFO 就绪
                end
            end
            WRITE_sci_data_len2: begin
                if (fifo_tready) begin
                    next_state = WRITE_time_code;
                end else begin
                    next_state = WRITE_sci_data_len2; // 等待 FIFO 就绪
                end
            end
            WRITE_time_code: begin
                if (time_code_cnt == 3'd5 && fifo_tready) begin
                    next_state = WRITE_N; 
                end else begin
                    next_state = WRITE_time_code; // 继续写入数据
                end
            end
            WRITE_N: begin
                if (write_n_cnt == (sci_data_len >> 3) - 1 && fifo_tready) begin
                    next_state = WRITE_sum1; // 写入 3 个数据，实际中应该是sci_data_len的[15:3]
                end else begin
                    next_state = WRITE_N; // 继续写入数据
                end
            end
            WRITE_sum1: begin
                if (fifo_tready) begin
                    next_state = WRITE_sum2;
                end else begin
                    next_state = WRITE_sum1;
                end
            end
            WRITE_sum2: begin
              if(fifo_tready) begin
                    next_state = DONE;
                end else begin
                    next_state = WRITE_sum2; // 继续写入数据
                end
            end
            DONE: begin
                if(fifo_tready) begin
                    next_state = WAIT;
                end else begin
                    next_state = DONE; // 继续写入数据
                end
            end
            WAIT: begin
              if (sci_gen == 16'h00) begin
                  next_state = INI;
              end
              else if(time_cnt >= wait_time_reg) begin
                  if (pack_num_reg != 0 && cnt >= pack_num_reg - 1) begin
                      next_state = INI;
                  end
                  else if(pack_num_reg == 0 || cnt < pack_num_reg - 1) begin
                          next_state = START;
                       end                  
              end else begin
                  next_state = WAIT;
              end
            end
            default: begin
                next_state = INI; // 默认回到 INI
            end
        endcase
    end

    // 输出逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            fifo_tvalid <= 1'b0;
            fifo_tdata <= 8'b0;
            pack_cnt <= 1'b0;
            time_code_cnt <= 3'b0;
            write_n_cnt <= 3'b0;
            sum_check <= 16'b0;
            sci_data <= 8'b0;
            cnt <= 16'b0;
            s_axis_tid <=1'b0;
            fifo_tlast <= 1'b0;
            pack_num_reg <=32'b0;
            sci_data_len_reg <= 16'b0;
            wait_time_reg <= 32'b0;
            pl_insight_reg <= 11'b0;
        end else begin
            case (current_state)
                INI: begin
                    fifo_tvalid <= 1'b0;
                    fifo_tlast <=1'b0;
                    time_cnt <= 23'd0;
                    if(sci_gen_valid) begin
                        // 只在第一次进入时保存参数
                        pack_num_reg <= pack_num;
                        sci_data_len_reg <= sci_data_len;
                        wait_time_reg <= wait_time;
                        pl_insight_reg <= pl_insight;
                        cnt <= 16'd0;  // 重置计数器
                    end else if(sci_gen == 16'h00) begin
                        // 当sci_gen变为0时清除寄存器
                        pack_num_reg <= 32'd0;
                        sci_data_len_reg <= 16'd0;
                        wait_time_reg <= 32'd0;
                        pl_insight_reg <= 11'd0;
                        cnt <= 16'd0;
                    end
                end
                START: begin
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1;
                        fifo_tlast <=1'b0;
                        fifo_tdata <= 8'hFD;
                        s_axis_tid <= 1'b1;
                    end 
                end
                WRITE_EB: begin
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1;
                        fifo_tdata <= 8'hEB;
                        s_axis_tid <= 1'b0;
                    end 
                end
                WRITE_90:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1; // 写入 0x90
                        fifo_tdata <= 8'h90;
                    end 
                WRITE_ver_and_sig1:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1; // 写入 版本号和包标识
                        fifo_tdata <= {5'b00001, pl_insight[10:8]};
                    end 
                WRITE_ver_and_sig2:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1; // 写入 版本号和包标识
                        fifo_tdata <= pl_insight[7:0];
                    end 
                WRITE_pack_ctrl1: 
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1;
                        fifo_tdata <= {2'b11, pack_cnt[13:8]};
                    end 
                WRITE_pack_ctrl2: 
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1;
                        fifo_tdata <= pack_cnt[7:0];
                        pack_cnt <= pack_cnt+1;
                    end 
                WRITE_sci_data_len1:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1; 
                        fifo_tdata <= {sci_data_len[15:11]};
                    end 
                WRITE_sci_data_len2:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1;
                        fifo_tdata <= {sci_data_len[10:3]+ 8'd7};
                    end 
                WRITE_time_code:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1; // 写入 时间码
                        fifo_tdata <= 8'b0; // 8个0
                        time_code_cnt <= time_code_cnt + 1;
                        sum_check <= {sum_check + 8'b0};
                        if (time_code_cnt == 3'd5)
                             time_code_cnt <= 3'b0;
                    end
                WRITE_N:
                    if (fifo_tready) begin
                        fifo_tvalid <= 1'b1;
                        fifo_tdata <= sci_data;
                        sum_check <= {sum_check + sci_data};
                        sci_data <= sci_data + 1;
                        if(write_n_cnt == (sci_data_len_reg >> 3) - 1) begin
                            write_n_cnt <= 2'd0; 
                        end else begin
                            write_n_cnt <= write_n_cnt + 1;
                        end
                    end
                DONE: begin
                  if (fifo_tready) begin
                    fifo_tvalid<= 1'b1;
                    fifo_tdata <= 8'hFB;
                    s_axis_tid <= 1'b1;
                    fifo_tlast <= 1'b1;
                  end
                end
               WAIT: begin
                    fifo_tvalid <= 1'b0;
                    fifo_tlast <= 1'b0;
                    if(time_cnt >= wait_time_reg) begin
                        if (pack_num_reg != 0 && cnt >= pack_num_reg) begin
                            cnt <= 16'd0;
                        end else begin
                            cnt <= cnt + 1;  
                        end
                        time_cnt <= 23'd0;
                    end else begin
                        time_cnt <= time_cnt + 1;
                    end
                end
                default: begin
                    fifo_tvalid <= 1'b0;
                    fifo_tdata <= 8'b0;
                end
            endcase
        end
    end
endmodule