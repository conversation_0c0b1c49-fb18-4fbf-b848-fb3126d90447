`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2025/03/04 10:33:40
// Design Name: 
// Module Name: science_data_tx
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 
// 
// Dependencies: 
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////

module sci_data_8b10btx 
(

    input clk,          // 40MHz 系统时钟
    input rst_n,        // 复位信号（低电平有效）
    input [31:0]wait_time,
    input [8:0] axis_tdata,   // 输入数据
    input axis_tvalid,        // fifo不为空指示位
    input axis_tlast1,
    output reg axis_tready,    //读fifo使能
    output reg lvds_data      // LVDS数据输出
);

// 内部信号定义
reg [1:0] read_fifo_state;
reg [8:0] frame_buffer;      // 9位宽的FIFO数据缓冲区
reg [3:0] convert_cnt;

reg [1:0] sending_state;
reg [8:0] wait_send;
reg [3:0] sending_cnt;

reg [8:0] datain;
wire [9:0] dataout;

// 定义 K28.5 的值
parameter K28_5 = 8'b10111100;

reg [31:0] wait_cnt;
reg [1:0] wait_cnt_state;

always @(posedge clk or negedge rst_n)begin
    if(!rst_n) begin
        wait_cnt_state<=2'd0;
        wait_cnt<=32'd0;
        end
    else begin
        case(wait_cnt_state)//初始；发送；等待；
            2'b00:
                if ((axis_tlast1==1'b1)&(axis_tready)&(axis_tvalid))
                    wait_cnt_state<=2'b01;
            2'b01: 
                if(convert_cnt=='d8)
                    wait_cnt_state<=2'b10;
                else
                    wait_cnt_state<=2'b01;
            2'b10:
                if(wait_cnt==wait_time-32'd1) begin
                   wait_cnt_state<=2'b00;
                   wait_cnt<=32'd0;
                  end
                else
                   wait_cnt<=wait_cnt+32'd1;
            default:
                   wait_cnt_state<=2'd0;
         endcase     
    end
end

// FIFO读使能信号生成逻辑
always@(posedge clk or negedge rst_n) begin
    if(~rst_n) begin
        axis_tready <= 0;
    end
    else begin
        if(((convert_cnt=='d8))&(axis_tvalid)&(!axis_tready)&(wait_cnt_state==2'b0)) axis_tready <= 1;
        else axis_tready <= 0;
    end
end


// FIFO读取逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        read_fifo_state <= 2'd0;
        convert_cnt <= 4'd0;
    end else begin
        case (read_fifo_state)
            2'd0: begin
                if (axis_tready) begin
                    // 如果数据有效fifo数据开始编码
                    frame_buffer <=axis_tdata[8:0] ;
                    convert_cnt <= 4'd0;
                    read_fifo_state <= read_fifo_state + 1;
                end else begin
                    // 如果为空，则读fifo数据开始发送
                    frame_buffer <= {1'b1,K28_5};
                    convert_cnt <= 4'd0;
                    read_fifo_state <= read_fifo_state + 1;
                end
            end
            2'd1: begin
                if (convert_cnt == 4'd0) begin
                    datain <= frame_buffer;
                    convert_cnt <= convert_cnt + 1;
                end else if (convert_cnt == 4'd8) begin
                    read_fifo_state <= 2'd0;
                end else begin
                    convert_cnt <= convert_cnt + 1;
                end
            end
        endcase
    end
end

// 发送数据通过LVDS
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        sending_state <= 2'd0;
        sending_cnt <= 4'd0;
    end else begin
        case (sending_state)
            2'd0: begin
                if ((read_fifo_state == 2'd1) && (convert_cnt == 4'd0)) begin
                    wait_send <= dataout[8:0];
                    sending_state <= sending_state + 1;
                    lvds_data <= dataout[9];
                    sending_cnt <= 4'd0;
                end
            end
            2'd1: begin
                if (sending_cnt == 4'd8) begin
                    sending_state <= 2'd0;
                    sending_cnt <= 4'd0;
                    lvds_data <= wait_send[8];
                    wait_send <= {wait_send[7:0], 1'b0};
                end else begin
                    sending_cnt <= sending_cnt + 1;
                    lvds_data <= wait_send[8];
                    wait_send <= {wait_send[7:0], 1'b0};
                end
            end
        endcase
    end
end

wire [1:0] _rd;
// 8b/10b编码器实例化
encoder_8b10b v_encoder_8b10b (
    .data_in(datain[7:0]),
    .clk(clk),
    .rst(rst_n),
    .tk(datain[8]),  // 1k
    .rd_in(_rd),
    .data_out(dataout),
    .rd_out(_rd),
    .en_data_in(1'b1)
);


endmodule

