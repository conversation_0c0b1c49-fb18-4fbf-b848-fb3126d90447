`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    21:21:55 10/16/2013 
// Design Name: 
// Module Name:    k_encoder 
// Project Name: 
// Target Devices: 
// Tool versions: 
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module k_encoder(data_in,rd_in,clk,rst,data_out,rd_out,en_data_in
    );
input  [7:0]  data_in;
input  en_data_in;
input  [1:0]  rd_in;
input  clk;
input  rst;
output  [1:0]  rd_out;
output  [9:0]  data_out;

 reg  [1:0]  rd_out_i;
 reg  [9:0]  data_out_i;
 reg  [1:0]  rd_out;
 reg  [9:0]  data_out;
 reg  [7:0]  data_in_i;
 reg  [1:0]  rd_in_i;

always @(posedge clk)	begin
      if (!rst) begin
      rd_out_i <= 2'b0;
      data_out_i <= 10'b0;
	   end
	   else if(en_data_in) begin
		   if (rd_in_i==2'b00)	begin
			case(data_in_i)
			8'b000_11100 : begin data_out_i <= 10'b0011110100;rd_out_i <= 2'b00;end
			8'b001_11100 : begin data_out_i <= 10'b0011111001;rd_out_i <= 2'b01;end
			8'b010_11100 : begin data_out_i <= 10'b0011110101;rd_out_i <= 2'b01;end
			8'b011_11100 : begin data_out_i <= 10'b0011110011;rd_out_i <= 2'b01;end
			8'b100_11100 : begin data_out_i <= 10'b0011110010;rd_out_i <= 2'b00;end
			8'b101_11100 : begin data_out_i <= 10'b0011111010;rd_out_i <= 2'b01;end
			8'b110_11100 : begin data_out_i <= 10'b0011110110;rd_out_i <= 2'b01;end
			8'b111_11100 : begin data_out_i <= 10'b0011111000;rd_out_i <= 2'b00;end
			8'b111_10111 : begin data_out_i <= 10'b1110101000;rd_out_i <= 2'b00;end
			8'b111_11011 : begin data_out_i <= 10'b1101101000;rd_out_i <= 2'b00;end
			8'b111_11101 : begin data_out_i <= 10'b1011101000;rd_out_i <= 2'b00;end
			8'b111_11110 : begin data_out_i <= 10'b0111101000;rd_out_i <= 2'b00;end
			default : begin data_out_i <= 10'b0000000000;rd_out_i <= 2'b00;end
			endcase
	      end
     else if (rd_in_i==2'b01) begin
			case(data_in_i)
			8'b000_11100 : begin data_out_i <= 10'b1100001011;rd_out_i <= 2'b01;end
			8'b001_11100 : begin data_out_i <= 10'b1100000110;rd_out_i <= 2'b00;end
			8'b010_11100 : begin data_out_i <= 10'b1100001010;rd_out_i <= 2'b00;end
			8'b011_11100 : begin data_out_i <= 10'b1100001100;rd_out_i <= 2'b00;end
			8'b100_11100 : begin data_out_i <= 10'b1100001101;rd_out_i <= 2'b01;end
			8'b101_11100 : begin data_out_i <= 10'b1100000101;rd_out_i <= 2'b00;end
			8'b110_11100 : begin data_out_i <= 10'b1100001001;rd_out_i <= 2'b00;end
			8'b111_11100 : begin data_out_i <= 10'b1100000111;rd_out_i <= 2'b01;end
			8'b111_10111 : begin data_out_i <= 10'b0001010111;rd_out_i <= 2'b01;end
			8'b111_11011 : begin data_out_i <= 10'b0010010111;rd_out_i <= 2'b01;end
			8'b111_11101 : begin data_out_i <= 10'b0100010111;rd_out_i <= 2'b01;end
			8'b111_11110 : begin data_out_i <= 10'b1000010111;rd_out_i <= 2'b01;end
			default : begin data_out_i <= 10'b0000000000;rd_out_i <= 2'b00;end
			endcase
	      end
	  else if (rd_in_i==2'b11) begin
			case(data_in_i)
			8'b000_11100 : begin data_out_i <= 10'b0011110100;rd_out_i <= 2'b11;end
			8'b001_11100 : begin data_out_i <= 10'b0011111001;rd_out_i <= 2'b00;end
			8'b010_11100 : begin data_out_i <= 10'b0011110101;rd_out_i <= 2'b00;end
			8'b011_11100 : begin data_out_i <= 10'b0011110011;rd_out_i <= 2'b00;end
			8'b100_11100 : begin data_out_i <= 10'b0011110010;rd_out_i <= 2'b11;end
			8'b101_11100 : begin data_out_i <= 10'b0011111010;rd_out_i <= 2'b00;end
			8'b110_11100 : begin data_out_i <= 10'b0011110110;rd_out_i <= 2'b00;end
			8'b111_11100 : begin data_out_i <= 10'b0011111000;rd_out_i <= 2'b11;end
			8'b111_10111 : begin data_out_i <= 10'b1110101000;rd_out_i <= 2'b11;end
			8'b111_11011 : begin data_out_i <= 10'b1101101000;rd_out_i <= 2'b11;end
			8'b111_11101 : begin data_out_i <= 10'b1011101000;rd_out_i <= 2'b11;end
			8'b111_11110 : begin data_out_i <= 10'b0111101000;rd_out_i <= 2'b11;end
			default : begin data_out_i <= 10'b0000000000;rd_out_i <= 2'b00;end
			endcase
			end
	  end else begin
	      rd_out_i <= 2'b0;
         data_out_i <= 10'b0;
			end
end

always @(posedge clk)	begin
      rd_out <= rd_out_i;
      data_out <= data_out_i;
end
	
always @(posedge clk)	begin
      data_in_i <= data_in;
		rd_in_i   <= rd_in;
end

endmodule
