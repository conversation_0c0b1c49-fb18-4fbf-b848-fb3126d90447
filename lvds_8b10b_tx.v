`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// 公司: 
// 工程师: 
// 
// 创建日期: 2025/01/23
// 设计名称: 
// 模块名称: lvds_8b10b_tx
// 项目名称: 
// 目标器件: 
// 工具版本: 
// 描述: 单线制LVDS 8b/10b编码发送模块
//       输入8位数据，经过8b/10b编码后串行输出
//       使用类似AXI Stream的握手机制
//       内部生成10MHz时钟用于外部FIFO
// 
// 依赖: encoder_8b10b.v
// 
// 修订:
// 修订 0.01 - 文件创建
// 附加注释:
// 
//////////////////////////////////////////////////////////////////////////////////

module lvds_8b10b_tx (
    // 系统信号
    input  wire        clk,           // 100MHz系统时钟
    input  wire        rst_n,         // 复位信号（低电平有效）
    
    // 数据输入接口 (类似AXI Stream)
    input  wire [7:0]  data_in,       // 8位数据输入
    input  wire        data_valid,    // 数据有效信号
    output reg         data_ready,    // 数据准备信号
    
    // LVDS输出接口
    output reg         lvds_data,     // 1位串行数据输出
    output wire        wr_clk         // 10MHz时钟输出（给外部FIFO使用）
);

// 内部信号定义
reg [8:0] datain;           // 送给编码器的9位数据（8位数据+1位tk）
wire [9:0] dataout;         // 编码器输出的10位编码数据
wire [1:0] _rd;             // RD状态反馈信号

reg [1:0] state;            // 主状态机
reg [3:0] bit_cnt;          // 位计数器
reg [8:0] shift_reg;        // 移位寄存器（存储低9位）

// 状态定义
localparam IDLE      = 2'b00;   // 空闲状态，等待数据
localparam ENCODE    = 2'b01;   // 编码状态，等待编码完成
localparam SHIFT_OUT = 2'b10;   // 输出状态，串行输出10位数据

// 时钟生成信号
wire wr_clk_internal;       // 内部10MHz时钟
wire pll_locked;            // PLL锁定信号

// 时钟生成器实例化（生成10MHz时钟）
// 注意：需要根据实际FPGA平台调整时钟IP核名称
clk_wiz_10m u_clk_gen (
    .clk_in1(clk),              // 100MHz输入时钟
    .clk_out1(wr_clk_internal), // 10MHz输出时钟
    .reset(~rst_n),             // 复位信号
    .locked(pll_locked)         // 锁定指示
);

// 将内部时钟输出给外部使用
assign wr_clk = wr_clk_internal;

// 8b/10b编码器实例化（参考science_data_tx的使用方式）
encoder_8b10b u_encoder (
    .data_in(datain[7:0]),      // 8位数据输入
    .clk(wr_clk_internal),      // 10MHz时钟
    .rst(rst_n),                // 复位信号
    .tk(datain[8]),             // 控制字符标识（0=数据字符，1=控制字符）
    .rd_in(_rd),                // RD输入
    .data_out(dataout),         // 10位编码输出
    .rd_out(_rd),               // RD输出（反馈）
    .en_data_in(1'b1)           // 编码使能（固定为1）
);

// 主状态机（在10MHz时钟域工作）
always @(posedge wr_clk_internal or negedge rst_n) begin
    if (!rst_n) begin
        // 复位所有信号
        state <= IDLE;
        data_ready <= 1'b0;        // 复位时不准备接收数据
        datain <= 9'b0;
        shift_reg <= 9'b0;
        bit_cnt <= 4'b0;
        lvds_data <= 1'b0;
    end
    else if (pll_locked) begin      // PLL锁定后才开始工作
        case (state)
            IDLE: begin
                // 空闲状态：准备接收新数据
                data_ready <= 1'b1;
                lvds_data <= 1'b0;     // 空闲时输出0
                
                if (data_valid && data_ready) begin
                    // 握手成功，接收数据并开始编码
                    datain <= {1'b0, data_in}; // tk=0表示普通数据字符
                    data_ready <= 1'b0;        // 拉低ready信号
                    state <= ENCODE;
                    bit_cnt <= 4'b0;
                end
            end
            
            ENCODE: begin
                // 编码状态：等待8b/10b编码完成
                // 参考science_data_tx，编码器需要几个时钟周期的延迟
                if (bit_cnt == 4'd8) begin
                    // 编码完成，准备串行输出
                    shift_reg <= dataout[8:0];     // 存储低9位到移位寄存器
                    lvds_data <= dataout[9];       // 先输出最高位
                    state <= SHIFT_OUT;
                    bit_cnt <= 4'b0;
                end
                else begin
                    bit_cnt <= bit_cnt + 1'b1;     // 等待编码完成
                end
            end
            
            SHIFT_OUT: begin
                // 串行输出状态：逐位输出剩余的9位数据
                if (bit_cnt == 4'd8) begin
                    // 输出完成，返回空闲状态
                    state <= IDLE;
                    bit_cnt <= 4'b0;
                    lvds_data <= shift_reg[8];     // 输出最后一位
                end
                else begin
                    // 继续串行输出
                    lvds_data <= shift_reg[8];     // 输出当前最高位
                    shift_reg <= {shift_reg[7:0], 1'b0}; // 左移，低位补0
                    bit_cnt <= bit_cnt + 1'b1;
                end
            end
            
            default: begin
                // 默认状态，返回空闲
                state <= IDLE;
            end
        endcase
    end
    else begin
        // PLL未锁定时保持复位状态
        data_ready <= 1'b0;
        lvds_data <= 1'b0;
    end
end

endmodule
