module cam_lvds_tx_pkt(
    input clk,
    input rst_n,
    //用户接口
    input [7:0] data_in,
    input [31:0] size,
    input start,
    //握手信号
    input tx_valid,
    output tx_ready,
    output reg busy,
    output reg done,
    //phy层接口
    output lvds_tx_clk,
    output lvds_tx_data,
    output lvds_tx_stb_n,
    //debug
    output reg [2:0]send_st,
    output reg [24:0] wr_8b_fifo_cnt
);

// ******** 发送状态机 ********
// LVDS12.5M, ui_clk100M, tx_fifo 16*8b
localparam  SEND_IDLE  = 3'd0,
            SEND_DATA  = 3'd1,
            SEND_WAIT  = 3'd2,
            SEND_DELAY = 3'd3,  // 帧间延迟(合并原SEND_WAIT功能)
            SEND_DONE  = 3'd4;
//reg [2:0] send_st;

//发送最后一包数据时需要延迟，等待FIFO中的数据全部按照wr_clk慢时钟读取完毕
//reg [24:0] wr_8b_fifo_cnt; //写入异步FIFO的字节数
// 删除send_wait_cnt，统一使用send_delay_cnt

// 全景相机CADU帧参数：898字节帧，0.1ms间隔
// clk 100MHz，10ns需要延时10,000个周期 = 0.1ms
localparam  DELAY_CYCLE = 25'd10_000;      // 0.1ms延迟
reg  [24:0] send_delay_cnt ;  // 延迟计数

//串并转换模块
wire wr_clk;
wire tx_ready_bit;
wire tx_valid_bit;
wire [7:0] data_in_bit;

//异步FIFO
wire tx_fifo_full,tx_fifo_empty;

assign tx_ready = (send_st == SEND_DATA)&&(~tx_fifo_full) ? 1'b1 : 1'b0;
assign tx_valid_bit = ~tx_fifo_empty;

always@(posedge clk) begin
    if(~rst_n) begin
        send_st <= SEND_IDLE;
        busy <= 1'b0;
        done <= 1'b0;
        wr_8b_fifo_cnt <= 'b0;
        send_delay_cnt <= 'b0;
    end
    else begin
        case(send_st)
        SEND_IDLE: begin  //0
            send_delay_cnt <= 'b0;
            busy <= 1'b0;
            done <= 1'b0;
            if(start) begin
                busy <= 1'b1;
                wr_8b_fifo_cnt <= 'b0;
                send_st <= SEND_DATA;
            end
        end

        SEND_DATA: begin  //1
            // 数据传输
            if(tx_valid&&tx_ready) begin
                wr_8b_fifo_cnt <= wr_8b_fifo_cnt + 1'b1;
                // 简化：只判断总数据是否发送完毕
                if(wr_8b_fifo_cnt + 1'b1 == size) begin
                    send_st <= SEND_WAIT;  // 数据发送完毕，进入延迟状态
                end
            end
        end
        
        SEND_WAIT:begin //等待FIFO中的数据全部发完
             if(lvds_tx_stb_n)begin
                  send_st <= SEND_DELAY;
             end
        end

        SEND_DELAY: begin  //3 帧间延迟 
            send_delay_cnt <= send_delay_cnt + 1'b1;
            if(send_delay_cnt == DELAY_CYCLE) begin
                send_delay_cnt <= 25'd0;
                send_st <= SEND_DONE;     // 所有数据发送完毕
            end
        end

        SEND_DONE: begin  //4 发送完成
            busy <= 1'b0;
            done <= 1'b1;
            send_st <= SEND_IDLE;
        end

        default: begin
            send_st <= SEND_IDLE;
        end
        endcase
    end
end

lvds_tx_fifo lvds_tx_fifo (
    .rst(~rst_n),
    .wr_clk(clk),
    .rd_clk(wr_clk),
    .din(data_in),
    .wr_en(tx_ready&&tx_valid),
    .rd_en(tx_valid_bit&&tx_ready_bit),
    .dout(data_in_bit),
    .full(tx_fifo_full),
    .empty(tx_fifo_empty)
);

lvds_tx_bit u_lvds_tx_bit(
    .clk(clk),
    .rst_n(rst_n),
    //用户接口
    .tx_valid(tx_valid_bit),
    .data_in(data_in_bit),
    .wr_clk(wr_clk),
    .tx_ready(tx_ready_bit),
    //phy层接口
    .lvds_tx_clk(lvds_tx_clk),
    .lvds_tx_data(lvds_tx_data),
    .lvds_tx_stb_n(lvds_tx_stb_n)
);

endmodule


