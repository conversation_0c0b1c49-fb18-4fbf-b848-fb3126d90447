`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// 测试文件: tb_lvds_8b10b_tx.v
// 创建日期: 2025/01/23
// 描述: lvds_8b10b_tx模块的测试平台
//       测试8位数据输入，验证8b/10b编码和串行输出功能
//////////////////////////////////////////////////////////////////////////////////

module tb_lvds_8b10b_tx();

// 测试信号定义
reg         clk;            // 100MHz系统时钟
reg         rst_n;          // 复位信号
reg [7:0]   data_in;        // 8位测试数据
reg         data_valid;     // 数据有效信号
wire        data_ready;     // 数据准备信号
wire        lvds_data;      // LVDS串行输出
wire        wr_clk;         // 10MHz时钟输出

// 测试用变量
reg [7:0] test_data [0:7];  // 测试数据数组
integer i;                  // 循环变量
integer bit_count;          // 位计数器
reg [9:0] received_bits;    // 接收到的10位数据

// 时钟生成 - 100MHz
initial begin
    clk = 0;
    forever #5 clk = ~clk;  // 10ns周期 = 100MHz
end

// 被测模块实例化
lvds_8b10b_tx uut (
    .clk(clk),
    .rst_n(rst_n),
    .data_in(data_in),
    .data_valid(data_valid),
    .data_ready(data_ready),
    .lvds_data(lvds_data),
    .wr_clk(wr_clk)
);

// 初始化测试数据
initial begin
    test_data[0] = 8'h00;   // 测试数据0
    test_data[1] = 8'h55;   // 测试数据1 (01010101)
    test_data[2] = 8'hAA;   // 测试数据2 (10101010)
    test_data[3] = 8'hFF;   // 测试数据3 (11111111)
    test_data[4] = 8'h0F;   // 测试数据4
    test_data[5] = 8'hF0;   // 测试数据5
    test_data[6] = 8'h3C;   // 测试数据6
    test_data[7] = 8'hC3;   // 测试数据7
end

// 主测试流程
initial begin
    // 初始化信号
    rst_n = 0;
    data_in = 8'h00;
    data_valid = 0;
    bit_count = 0;
    received_bits = 10'b0;
    
    // 打印测试开始信息
    $display("========================================");
    $display("开始LVDS 8b/10b发送模块测试");
    $display("系统时钟: 100MHz, LVDS时钟: 10MHz");
    $display("========================================");
    
    // 复位序列
    $display("时间 %0t: 开始复位", $time);
    #100;
    rst_n = 1;
    $display("时间 %0t: 复位释放", $time);
    
    // 等待PLL锁定和模块准备
    wait(data_ready == 1'b1);
    $display("时间 %0t: 模块准备就绪", $time);
    
    // 测试多个数据
    for (i = 0; i < 8; i = i + 1) begin
        $display("----------------------------------------");
        $display("测试数据 %0d: 0x%02h (%08b)", i, test_data[i], test_data[i]);
        send_byte(test_data[i]);
        
        // 接收并验证串行数据
        receive_serial_data();
        
        // 等待一段时间再发送下一个数据
        repeat(20) @(posedge wr_clk);
    end
    
    $display("========================================");
    $display("测试完成");
    $display("========================================");
    
    #1000;
    $finish;
end

// 发送一个字节数据的任务
task send_byte;
    input [7:0] byte_data;
    begin
        // 等待模块准备就绪
        wait(data_ready == 1'b1);
        
        // 在wr_clk上升沿发送数据
        @(posedge wr_clk);
        data_in = byte_data;
        data_valid = 1'b1;
        $display("时间 %0t: 发送数据 0x%02h", $time, byte_data);
        
        // 等待握手完成
        @(posedge wr_clk);
        while (!(data_valid && data_ready)) begin
            @(posedge wr_clk);
        end
        
        // 握手完成后拉低valid信号
        @(posedge wr_clk);
        data_valid = 1'b0;
        $display("时间 %0t: 握手完成", $time);
    end
endtask

// 接收串行数据的任务
task receive_serial_data;
    begin
        bit_count = 0;
        received_bits = 10'b0;
        
        $display("时间 %0t: 开始接收串行数据", $time);
        
        // 等待开始输出数据（检测到第一个有效位）
        @(posedge wr_clk);
        while (data_ready == 1'b1) begin  // 等待离开IDLE状态
            @(posedge wr_clk);
        end
        
        // 接收10位串行数据
        for (bit_count = 0; bit_count < 10; bit_count = bit_count + 1) begin
            @(posedge wr_clk);
            received_bits[9-bit_count] = lvds_data;  // MSB先输出
            $display("时间 %0t: 接收位 %0d = %b", $time, bit_count, lvds_data);
        end
        
        $display("时间 %0t: 接收完成，10位数据: %010b (0x%03h)", 
                 $time, received_bits, received_bits);
    end
endtask

// 监控信号变化
always @(posedge wr_clk) begin
    if (rst_n) begin
        // 监控状态变化
        if ($time > 200) begin  // 跳过复位期间
            $display("时间 %0t: wr_clk上升沿 - data_ready=%b, data_valid=%b, lvds_data=%b", 
                     $time, data_ready, data_valid, lvds_data);
        end
    end
end

// 错误检测
always @(posedge clk) begin
    // 检测握手协议违规
    if (rst_n && data_valid && !data_ready) begin
        if ($time > 1000) begin  // 给足够时间让模块稳定
            $display("警告 %0t: 握手协议违规 - data_valid=1但data_ready=0", $time);
        end
    end
end

// 超时检测
initial begin
    #50000;  // 50us超时
    $display("错误: 测试超时！");
    $finish;
end

endmodule
