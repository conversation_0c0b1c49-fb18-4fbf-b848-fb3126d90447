`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2025/04/14 19:55:40
// Design Name: 
// Module Name: sci_8b10b_tx_new
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 
// 
// Dependencies: 
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////


module sci_8b10b_tx_new(                 
    input wire clk_40M, //修改了时钟
    input wire clk_50M, //CPU_IF 50mhz
    input wire rst_n, //上层复位信号
    input wire cpu_wen,
    input wire [7:0]  cpu_addr,
    input wire [31:0] cpu_din ,

  
    output wire lvds_data


);
    // 内部信号声明
    (* MARK_DEBUG="true" *)wire [7:0] fifo_tdata;
    (* MARK_DEBUG="true" *)wire fifo_tvalid;
    (* MARK_DEBUG="true" *)wire fifo_tready;
    (* MARK_DEBUG="true" *)wire fifo_tlast;/////////////////////////////新加入


    (* MARK_DEBUG="true" *)wire [7:0] m_axis_tdata;
    (* MARK_DEBUG="true" *)wire m_axis_tvalid;
    (* MARK_DEBUG="true" *)wire m_axis_tready;
    (* MARK_DEBUG="true" *)wire m_axis_tlast;/////////////////////////////新加入

    (* MARK_DEBUG="true" *)wire wr_rst_busy;
    (* MARK_DEBUG="true" *)wire rd_rst_busy;
    (* MARK_DEBUG="true" *)wire [0:0] s_axis_tid;  // 8b10b释放相机模块的s_axis_tid信号
    (* MARK_DEBUG="true" *)wire [0:0] m_axis_tid;
    
    reg [31:0] setreg0; //开启关闭寄存器
    reg [31:0] setreg1; //发送次数寄存器
    reg [31:0] setreg2; //数据包间隔寄存器
    reg [31:0] setreg3; //应用过程标识及包数据域长度

   
    (* MARK_DEBUG="true" *)wire [10:0] pl_insight;  //= 11'b10101010101
    (* MARK_DEBUG="true" *)wire [15:0] sci_gen; //=16'hFF
    (* MARK_DEBUG="true" *)wire [31:0] pack_num; //= 32'd3;
    (* MARK_DEBUG="true" *)wire [31:0] wait_time; //= 32'd3;
    (* MARK_DEBUG="true" *)wire [15:0] sci_data_len; //=16'd24;
    (* MARK_DEBUG="true" *)wire [3:0] state_monitor;


   always @(posedge clk_50M or negedge rst_n) begin
     if(!rst_n) begin
        setreg0<=32'b0; //初始值
        setreg1<=32'b0; //初始值
        setreg2<=32'b100; //初始值
        setreg3<=32'b0; //初始值
      end
     else begin
        if(!cpu_wen) begin
            case(cpu_addr[1:0])
                2'b00: setreg0<=cpu_din;
                2'b01: setreg1<=cpu_din;
                2'b10: setreg2<=cpu_din;
                2'b11: setreg3<=cpu_din;
            endcase   
        end 
     end          
    end
    
  assign sci_gen=setreg0[31:24]; //可以生成数据
  assign pack_num=setreg1; //= 32'd3;
  assign wait_time=setreg2;
  assign pl_insight= setreg3[31:21];
  assign sci_data_len= setreg3[15:0];




data_gen_8b10b sci_data_gen (
    .pl_insight(pl_insight),
    .sci_gen(sci_gen),
    .sci_data_len(sci_data_len),
    .clk(clk_40M),
    .rst_n(rst_n),
    .pack_num(pack_num),
    .wait_time(wait_time),/////////////////////////////////////////改成wait_time
    .cpu_wen(cpu_wen),
    .cpu_addr(cpu_addr),
    .cpu_din(cpu_din),
    .state_monitor(state_monitor),
    .fifo_tdata(fifo_tdata),
    .fifo_tvalid(fifo_tvalid),
    .fifo_tready(fifo_tready),
    .fifo_tlast(fifo_tlast),
    .s_axis_tid(s_axis_tid)
    ///////////////////////////???????????????????????????????需要加入tlast
    
);

fifo_generator_1 fifo (
    .wr_rst_busy(wr_rst_busy),
    .rd_rst_busy(rd_rst_busy),
    .s_aclk(clk_40M),
    .s_aresetn(rst_n),
    .s_axis_tvalid(fifo_tvalid),
    .s_axis_tready(fifo_tready),
    .s_axis_tdata(fifo_tdata),
    .s_axis_tid(s_axis_tid),
    .s_axis_tlast(fifo_tlast),/////////////////////////////新加入
    
    .m_axis_tvalid(m_axis_tvalid),
    .m_axis_tready(m_axis_tready),
    .m_axis_tdata(m_axis_tdata),
    .m_axis_tid(m_axis_tid),
    .m_axis_tlast(m_axis_tlast)/////////////////////////////新加入
);

sci_data_8b10btx encoder (
    .clk(clk_40M),
    .rst_n(rst_n),
    .wait_time(wait_time),///////////////////////////////////////////////新加入
    .axis_tdata({m_axis_tid, m_axis_tdata}),
    .axis_tvalid(m_axis_tvalid),
    .axis_tready(m_axis_tready),
    .axis_tlast1(m_axis_tlast),///////////////////////////////////////新加入
    .lvds_data(lvds_data)
);

endmodule